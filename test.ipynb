print("ddd")

import requests
from bs4 import BeautifulSoup

import requests

# 登入網站的URL
url = "http://************:31539/login"

# 登入表單的資料
data = {
    "username": "your_username",
    "password": "999"
}

# 發送POST請求
response = requests.post(url, data=data)

# 如果登入成功，會得到一個Cookie
if response.status_code == 200:
    cookie = response.cookies
    print("登入成功！")
else:
    print("登入失敗！")

import requests

# 登入網站的URL
url = "http://************:31539/login"

# 登入JSON資料
data = {
    "username": "your_username",
    "password": "your_password"
}

# 發送POST請求
response = requests.post(url, json=data)

# 如果登入成功，會得到一個Cookie
if response.status_code == 200:
    cookie = response.cookies
    print("登入成功！")
else:
    print("登入失敗！")

import requests
from bs4 import BeautifulSoup

# 登入網站
url = "http://************:31539/login"
data = {"username": "admin", "password": "pega#1234"}
response = requests.post(url, data=data)

# 取得Cookie
cookie = response.cookies
response
# # 發送資料請求
# url = "http://************:31539/data"
# response = requests.get(url, cookies=cookie)

# # 解析HTML
# soup = BeautifulSoup(response.content, "html.parser")

# # 取得資料
# data = soup.find("table", {"id": "data-table"}).text
# print(data)

import requests
from bs4 import BeautifulSoup

url = "http://************:31539/substrate/overall"
response = requests.get(url)

if response.status_code == 200:
    soup = BeautifulSoup(response.text, 'html.parser')
    print(soup.prettify())
else:
    print("Failed to retrieve the webpage")

pip install scrapy